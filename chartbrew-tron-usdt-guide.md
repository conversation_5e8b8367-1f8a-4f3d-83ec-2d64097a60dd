# 使用 Chartbrew 创建 Tron USDT 钱包数据可视化面板

本指南将帮助你使用 [Chartbrew](https://chartbrew.com) 快速构建一个数据可视化面板，用于展示 TRON 链上某个钱包地址的 USDT（TRC-20）资产余额和转账历史，并将图表嵌入你的网站。

---

## 🧰 所需条件

- 一个 Chartbrew 账户：[注册 Chartbrew](https://app.chartbrew.com)
- 一个可访问的 Tron 钱包地址（例如：`TXYZ...`）
- 可以访问 Tron API（无需 API Key）

---

## 🧱 项目结构规划

你将创建的面板结构如下：

- 🔵 图表 1：当前 USDT 余额（柱状图/卡片）
- 🟢 图表 2：转账历史（折线图）
- 🟡 图表 3：USDT 收入 vs 支出（饼图）
- 🔗 所有图表可 iframe 嵌入到你的前端页面中

---

## 🚀 步骤一：创建 Chartbrew 项目

1. 登录 Chartbrew 控制台：[https://app.chartbrew.com](https://app.chartbrew.com)
2. 点击 `+ New Project` → 输入项目名称：`Tron USDT Viewer`
3. 创建后进入项目设置界面

---

## 🔌 步骤二：添加 Tron REST API 数据源

1. 点击左侧菜单的 `Connections` → `+ New Connection`
2. 配置如下：

```
Name: TronGrid
Type: REST API
Base URL: https://api.trongrid.io
```

3. 保存连接

---

## 📊 步骤三：添加 USDT 余额查询图表

1. 新建图表 → 命名为 “USDT Balance”
2. 选择上一步的连接
3. 设置请求路径：
```
GET /v1/accounts/<WALLET_ADDRESS>/assets/trc20
```
4. 响应结构中找到：
```json
[
  {
    "token_id": "TXLAQ63Xg1NAzckPwKHvzw7CSEmLMEqcdj",
    "balance": "*********",
    "symbol": "USDT"
  }
]
```
5. 设置字段映射：
   - `symbol` 作为图表分类
   - `balance` 映射为数值（记得除以 10^6）

---

## 📈 步骤四：添加交易记录图表

1. 新建图表 → 命名为 “USDT Transfer History”
2. 请求路径：
```
GET https://apilist.tronscanapi.com/api/transfer/trc20?address=<WALLET_ADDRESS>&limit=10
```
3. 映射字段：
   - `block_ts`（时间戳） → X 轴
   - `quant`（交易数量） → Y 轴

4. 设置时间格式为 readable date

---

## 🌐 步骤五：图表嵌入你的网站

1. 打开任意图表 → 点击右上角 “Share”
2. 获取 iframe 嵌入代码：
```html
<iframe src="https://app.chartbrew.com/embed/chart/abc123" width="100%" height="400" frameborder="0"></iframe>
```
3. 粘贴到你的网站 HTML 中，如：
```html
<div class="my-chart-container">
  <h3>USDT Wallet Balance</h3>
  <iframe src="..." ...></iframe>
</div>
```

---

## 🛠 高级建议

| 场景 | 建议 |
|------|------|
| 多钱包比较 | 使用多个 API 请求分别绑定不同钱包地址 |
| 数据刷新 | 设置图表定时刷新，如每小时自动拉取 |
| 后端整合 | 可用 Node.js/FastAPI 拉取 Tron 数据并标准化为 Chartbrew 接口 |
| 私有部署 | Chartbrew 支持 Docker 自部署，保障数据隐私

---

## ✅ 示例地址

Chartbrew Demo: https://demo.chartbrew.com  
TronGrid API 文档: https://developers.tron.network/docs/account

---

如果你需要我帮你生成前端代码并自动嵌入这些图表 iframe，请继续告诉我！
